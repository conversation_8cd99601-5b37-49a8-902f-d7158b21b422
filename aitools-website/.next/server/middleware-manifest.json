{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3k8AEwPipuwUgauKLO/KQ0DvRXTLZzY+2yJcVLrgW30=", "__NEXT_PREVIEW_MODE_ID": "b780bdc9c0c9ef482ac5b212090bc71e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "49aee8b0407f4ef14470c1406ff259b14012ef999b86d25d9f1cbeec5d86ce44", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d2119a25f0f824a69ffcc05af0927794a3da061d274f0a84f71cc51db9893ffd"}}}, "sortedMiddleware": ["/"], "functions": {}}