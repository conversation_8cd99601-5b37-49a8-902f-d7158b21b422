{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3k8AEwPipuwUgauKLO/KQ0DvRXTLZzY+2yJcVLrgW30=", "__NEXT_PREVIEW_MODE_ID": "7785de0e84943a788abea3363ec452ce", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84b8eb910d83a07772d66cb769e21efe93a120aba2d3fc06b70ed1b3eeadf532", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d7262f4bd0b5d00953d9eaaf0ee160f0446a6169720c924dc22bb79e52f2e76a"}}}, "sortedMiddleware": ["/"], "functions": {}}