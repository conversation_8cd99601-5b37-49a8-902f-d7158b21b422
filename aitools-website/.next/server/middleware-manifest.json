{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_3fa1dad3._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__fc6fa216._.js", "server/edge/chunks/edge-wrapper_86ad20df.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3k8AEwPipuwUgauKLO/KQ0DvRXTLZzY+2yJcVLrgW30=", "__NEXT_PREVIEW_MODE_ID": "604251053453d945fa34499008e6150e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b4234b932bd5115a8a15812c8e3ced884cebd7e7875c1859a40251df948ab854", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "48137642008b46d919b9f856b1d57fbbadf2f96bfaefd045dbccc6179842ed62"}}}, "sortedMiddleware": ["/"], "functions": {}}