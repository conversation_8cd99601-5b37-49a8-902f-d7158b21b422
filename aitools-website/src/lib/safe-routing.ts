'use client';

// 安全路由助手 - 包装next-intl路由功能，提供额外的URL安全检查

import React from 'react';
import { useRouter as useNextIntlRouter, usePathname as useNextIntlPathname } from '@/i18n/routing';
import { useLocale } from 'next-intl';
import { normalizeUrlPath, buildSafeUrl, validateAndFixUrl } from './url-utils';

/**
 * 增强的useRouter hook，提供安全的路由导航
 */
export function useSafeRouter() {
  const router = useNextIntlRouter();
  const locale = useLocale();

  return {
    ...router,
    
    /**
     * 安全的push方法，自动处理locale前缀
     */
    push: (href: string, options?: any) => {
      const normalizedHref = normalizeUrlPath(href, locale);
      console.log(`🔄 Safe Router Push: ${href} -> ${normalizedHref}`);
      return router.push(normalizedHref, options);
    },
    
    /**
     * 安全的replace方法，自动处理locale前缀
     */
    replace: (href: string, options?: any) => {
      const normalizedHref = normalizeUrlPath(href, locale);
      console.log(`🔄 Safe Router Replace: ${href} -> ${normalizedHref}`);
      return router.replace(normalizedHref, options);
    },
    
    /**
     * 安全的prefetch方法，自动处理locale前缀
     */
    prefetch: (href: string, options?: any) => {
      const normalizedHref = normalizeUrlPath(href, locale);
      return router.prefetch(normalizedHref, options);
    },
    
    /**
     * 构建安全的URL
     */
    buildUrl: (path: string, params?: Record<string, string | number>) => {
      return buildSafeUrl(path, locale, params);
    },
    
    /**
     * 验证当前URL并在需要时重定向
     */
    validateCurrentUrl: () => {
      if (typeof window !== 'undefined') {
        const currentUrl = window.location.pathname + window.location.search;
        const { fixedUrl, needsRedirect } = validateAndFixUrl(currentUrl, locale);
        
        if (needsRedirect) {
          console.log(`🔄 URL Validation Redirect: ${currentUrl} -> ${fixedUrl}`);
          router.replace(fixedUrl);
          return true;
        }
      }
      return false;
    }
  };
}

/**
 * 增强的usePathname hook，提供安全的路径名获取
 */
export function useSafePathname() {
  const pathname = useNextIntlPathname();
  const locale = useLocale();
  
  return {
    /**
     * 获取当前路径名
     */
    pathname,
    
    /**
     * 获取规范化的路径名
     */
    normalizedPathname: normalizeUrlPath(pathname, locale),
    
    /**
     * 检查当前路径是否需要规范化
     */
    needsNormalization: pathname !== normalizeUrlPath(pathname, locale),
    
    /**
     * 获取不含locale前缀的路径
     */
    pathWithoutLocale: pathname.replace(new RegExp(`^/${locale}`), '') || '/'
  };
}

/**
 * 安全的window.location.href设置函数
 */
export function safeLocationHref(url: string, currentLocale?: string) {
  if (typeof window === 'undefined') return;
  
  const locale = currentLocale || 'en';
  const { fixedUrl } = validateAndFixUrl(url, locale);
  
  console.log(`🔄 Safe Location Href: ${url} -> ${fixedUrl}`);
  window.location.href = fixedUrl;
}

/**
 * 创建安全的链接href
 */
export function createSafeHref(path: string, locale?: string, params?: Record<string, string | number>): string {
  return buildSafeUrl(path, locale, params);
}

/**
 * React Hook：在组件挂载时验证URL
 */
export function useUrlValidation() {
  const router = useSafeRouter();
  
  // 在客户端挂载时验证URL
  if (typeof window !== 'undefined') {
    // 使用setTimeout确保在React hydration完成后执行
    setTimeout(() => {
      router.validateCurrentUrl();
    }, 0);
  }
}

/**
 * 高阶组件：为组件添加URL验证功能
 */
export function withUrlValidation<P extends object>(Component: React.ComponentType<P>) {
  return function UrlValidatedComponent(props: P) {
    useUrlValidation();
    return React.createElement(Component, props);
  };
}
