import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { NextRequest, NextResponse } from 'next/server';

// 支持的语言列表
const locales = ['en', 'zh'];
const defaultLocale = 'en';

// URL规范化函数
function normalizeUrl(pathname: string): { normalized: string; needsRedirect: boolean } {
  // 移除开头和结尾的斜杠，然后分割路径
  const segments = pathname.replace(/^\/+|\/+$/g, '').split('/').filter(Boolean);

  // 如果路径为空，返回默认语言
  if (segments.length === 0) {
    return { normalized: `/${defaultLocale}`, needsRedirect: true };
  }

  const firstSegment = segments[0];

  // 检查是否有重复的locale前缀
  if (locales.includes(firstSegment)) {
    const secondSegment = segments[1];

    // 如果第二个段也是locale，说明有重复
    if (secondSegment && locales.includes(secondSegment)) {
      // 移除第一个重复的locale，保留第二个
      const cleanedSegments = segments.slice(1);
      const normalized = '/' + cleanedSegments.join('/');
      return { normalized, needsRedirect: true };
    }

    // 如果只有一个locale前缀，检查是否完整
    const normalized = '/' + segments.join('/');
    return { normalized, needsRedirect: false };
  }

  // 如果没有locale前缀，添加默认locale
  const normalized = `/${defaultLocale}/${segments.join('/')}`;
  return { normalized, needsRedirect: true };
}

// 自定义middleware
function customMiddleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;

  console.log(`🔍 Middleware processing: ${pathname}`);
  console.log(`🔍 Request URL: ${request.url}`);

  // 跳过API路由和静态资源
  if (
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico' ||
    pathname === '/robots.txt' ||
    pathname === '/sitemap.xml' ||
    pathname.startsWith('/uploads')
  ) {
    console.log(`⏭️ Skipping static resource: ${pathname}`);
    return NextResponse.next();
  }

  // 规范化URL
  const { normalized, needsRedirect } = normalizeUrl(pathname);

  console.log(`🔧 URL normalization result:`, {
    original: pathname,
    normalized,
    needsRedirect
  });

  // 如果需要重定向，执行重定向
  if (needsRedirect) {
    const redirectUrl = new URL(normalized + search, request.url);
    console.log(`🔄 URL Redirect: ${pathname} -> ${normalized}`);
    return NextResponse.redirect(redirectUrl, 307);
  }

  // 如果URL已经规范化，继续处理
  console.log(`✅ URL is valid, passing to next-intl middleware`);
  return NextResponse.next();
}

// 组合自定义middleware和next-intl middleware
export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log(`🔍 Main middleware processing: ${pathname}`);

  // 跳过API路由和静态资源
  if (
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico' ||
    pathname === '/robots.txt' ||
    pathname === '/sitemap.xml' ||
    pathname.startsWith('/uploads')
  ) {
    console.log(`⏭️ Skipping static resource: ${pathname}`);
    return NextResponse.next();
  }

  // 首先运行自定义URL规范化
  const customResponse = customMiddleware(request);

  // 如果自定义middleware返回了重定向，直接返回
  if (customResponse && customResponse.status === 307) {
    return customResponse;
  }

  // 否则运行next-intl middleware
  const intlMiddleware = createMiddleware(routing);
  return intlMiddleware(request);
}

export const config = {
  // 匹配所有路径，除了以下路径：
  // - API 路由 (/api)
  // - 静态文件 (_next/static)
  // - 图片文件 (_next/image)
  // - favicon.ico
  // - robots.txt
  // - sitemap.xml
  // - uploads 目录
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'
  ]
};
